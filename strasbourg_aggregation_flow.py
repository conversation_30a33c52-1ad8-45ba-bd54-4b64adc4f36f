#!/usr/bin/env python3
"""
4-Level Aggregation/Disaggregation Sankey Diagram for Strasbourg Mobility Flows
Demonstrates hierarchical flow visualization with aggregation→disaggregation pattern.
"""

import os
import pandas as pd
from pathlib import Path
from mobility.serializers.charters.flow_chart_grapher import FlowChartGrapher
from cairosvg import svg2png
from wand.image import Image


def create_strasbourg_aggregation_flow(csv_file_path, output_suffix=""):
    """Create a combined figure showing the complete aggregation/disaggregation story.

    Args:
        csv_file_path (str or Path): Path to the CSV file to process
        output_suffix (str): Suffix to add to output filenames for batch processing

    Returns:
        tuple: (svg_output_path, png_output_path)
    """
    # Convert to Path object for robust path handling
    csv_path = Path(csv_file_path)

    # Output directory
    output_dir = Path(r"C:\Users\<USER>\Documents\affaires\23I711.3 Strasbourg OD PL FCD\spatial_analysis_output\aggregated")
    output_dir.mkdir(exist_ok=True)

    # Read CSV data
    print(f"Reading CSV data from: {csv_path}")
    df = pd.read_csv(csv_path)
    print(f"Loaded {len(df)} rows of flow data")

    # Helper function for department mapping
    def get_department(zone_name):
        if zone_name.startswith("Strasbourg"):
            return "Strasbourg"
        else:
            return zone_name

    # Create department-level aggregated flows
    df_dept = df.copy()
    df_dept["origin_dept"] = df_dept["origin_zone"].apply(get_department)
    df_dept["destination_dept"] = df_dept["destination_zone"].apply(get_department)
    dept_flows = (
        df_dept.groupby(["origin_dept", "destination_dept"])["count"]
        .sum()
        .reset_index()
    )

    # Prepare data for each chart

    # Chart 1: Strasbourg Communes → Strasbourg Department (Level 1→2) - REVERSED DIRECTION
    strasbourg_origins = df[df["origin_zone"].str.startswith("Strasbourg")].copy()
    level1_2_data = (
        strasbourg_origins.groupby("origin_zone")["count"].sum().reset_index()
    )

    level1_2_flows = []
    for _, row in level1_2_data.iterrows():
        # Reverse direction: commune → "Strasbourg"
        level1_2_flows.extend([(row["origin_zone"], "Strasbourg")] * row["count"])

    # Chart 2: Department Level Flows (Level 2→3)
    level2_3_flows = []
    for _, row in dept_flows.iterrows():
        level2_3_flows.extend(
            [(row["origin_dept"], row["destination_dept"])] * row["count"]
        )

    # Chart 3: Strasbourg Department → Strasbourg Communes (Level 3→4)
    strasbourg_destinations = df[
        df["destination_zone"].str.startswith("Strasbourg")
    ].copy()
    level3_4_data = (
        strasbourg_destinations.groupby("destination_zone")["count"].sum().reset_index()
    )

    level3_4_flows = []
    for _, row in level3_4_data.iterrows():
        level3_4_flows.extend([("Strasbourg", row["destination_zone"])] * row["count"])

    # Create grapher instance
    grapher = FlowChartGrapher[str](
        str(output_dir),
        main_title_font_size_px=26,  # Smaller main title for complex charts
        title_font_size_px=20,  # Smaller level titles
        line_header_font_size_px=14,  # Smaller category labels
        number_font_size_px=14,  # Smaller numbers for better fit
        column_width=140,  # Slightly wider columns for better text fitting
        columns_height=500,  # Increased height for complex labels
        categories_padding=38,  # More padding for category labels to prevent cutoff
        flow_width=225,  # Wider flows for better visual impact
    )

    # Generate colors and categories for each chart
    all_strasbourg = sorted(
        [z for z in df["origin_zone"].unique() if z.startswith("Strasbourg")]
    )
    all_departments = sorted(dept_flows["origin_dept"].unique())

    strasbourg_colors = {"Strasbourg": "#1f2e68"}
    color_palette = [
        "#E53935",
        "#1E88E5",
        "#43A047",
        "#9C27B0",
        "#FF5722",
        "#795548",
        "#607D8B",
    ]
    for i, commune in enumerate(all_strasbourg):
        strasbourg_colors[commune] = color_palette[i % len(color_palette)]

    dept_colors = {}
    dept_palette = [
        "#4CAF50",
        "#2196F3",
        "#FF9800",
        "#9C27B0",
        "#00BCD4",
        "#8BC34A",
        "#FFC107",
    ]
    for i, dept in enumerate(all_departments):
        if dept == "Strasbourg":
            dept_colors[dept] = "#1f2e68"
        else:
            dept_colors[dept] = dept_palette[i % len(dept_palette)]

    # Create label mappings
    strasbourg_labels = {
        k: k.replace("Strasbourg ", "").replace("-", "").strip() if k != "Strasbourg" else "Strasbourg"
        for k in all_strasbourg + ["Strasbourg"]
    }
    dept_labels = {k: k for k in all_departments}

    # Create individual chart components using the new composition approach
    print("Creating chart components...")

    # Chart 1 components
    flow1 = grapher.compute_flows(level1_2_flows, all_strasbourg + ["Strasbourg"])
    components1 = grapher.make_flow_components(
        flow1,
        ("", ""),
        strasbourg_colors,
        all_strasbourg + ["Strasbourg"],
        strasbourg_labels,
        {},
        x_offset=0,
    )

    # Chart 2 components
    flow2 = grapher.compute_flows(level2_3_flows, all_departments)
    components2 = grapher.make_flow_components(
        flow2,
        ("Origine", "Destination"),
        dept_colors,
        all_departments,
        dept_labels,
        {},
        x_offset=0,  # Will be positioned by compose_flow_charts
    )

    # Chart 3 components
    flow3 = grapher.compute_flows(level3_4_flows, ["Strasbourg"] + all_strasbourg)
    components3 = grapher.make_flow_components(
        flow3,
        ("", ""),
        strasbourg_colors,
        ["Strasbourg"] + all_strasbourg,
        strasbourg_labels,
        {},
        x_offset=0,  # Will be positioned by compose_flow_charts
    )

    # Compose the three charts into a single figure using the new composition method
    print("Composing charts into single figure...")

    # Use the new composition method to combine all components
    composed_figure = grapher.compose_flow_charts(
        flow_components_list=[components1, components2, components3],
        main_title="Flux OD PL Strasbourg RN4 Est-Ouest: Agrégation/Désagrégation Complète",
        chart_spacing=215,
        first_vertical_offset=575,
        last_vertical_offset=575,
    )

    # Generate dynamic output filenames
    base_filename = f"strasbourg_composed_aggregation_flow{output_suffix}"
    svg_filename = f"{base_filename}.svg"
    png_filename = f"{base_filename}.png"

    # Write the composed figure
    svg_output_path = grapher.writer.write_svg(composed_figure, svg_filename)

    # Convert SVG output path to Path object for robust handling
    svg_path = os

    # Generate PNG filename by changing extension
    png_path = svg_path.with_suffix('.png')

    # Convert SVG to PNG using Wand with proper path handling
    print(f"Converting SVG to PNG: {svg_path} -> {png_path}")
    try:
        img = Image(filename=os.path.join(output_dir, svg_path.name))
        img.save(filename=os.path.join(output_dir, png_path.name))
        print(f"PNG conversion successful: {png_path}")
    except Exception as e:
        print(f"Warning: PNG conversion failed: {e}")
        png_path = None

    print(f"Composed aggregation/disaggregation flow chart generated: {svg_path}")
    print("Chart shows complete flow pattern with single title:")
    print("  Left: Strasbourg Communes → Strasbourg Department")
    print("  Center: Department ↔ Department flows")
    print("  Right: Strasbourg Department → Strasbourg Communes")

    return str(svg_path), str(png_path) if png_path else None


def process_csv_folder(input_folder, output_dir):
    """Process all CSV files in a folder and generate flow charts for each.

    Args:
        input_folder (str or Path): Path to folder containing CSV files
        output_dir (str): Output directory for generated charts

    Returns:
        list: List of tuples (csv_file, svg_output, png_output) for each processed file
    """
    input_path = Path(input_folder)

    if not input_path.exists():
        raise ValueError(f"Input folder does not exist: {input_path}")

    if not input_path.is_dir():
        raise ValueError(f"Input path is not a directory: {input_path}")

    # Find all CSV files in the input folder
    csv_files = list(input_path.glob("*.csv"))

    if not csv_files:
        print(f"No CSV files found in: {input_path}")
        return []

    print(f"Found {len(csv_files)} CSV files to process in: {input_path}")

    results = []

    for csv_file in csv_files:
        print(f"\n{'='*60}")
        print(f"Processing: {csv_file.name}")
        print(f"{'='*60}")

        # Create suffix from CSV filename (without extension)
        suffix = f"_{csv_file.stem}"

        try:
            svg_output, png_output = create_strasbourg_aggregation_flow(
                csv_file,
                output_suffix=suffix
            )
            results.append((str(csv_file), svg_output, png_output))
            print(f"✓ Successfully processed: {csv_file.name}")

        except Exception as e:
            print(f"✗ Error processing {csv_file.name}: {e}")
            results.append((str(csv_file), None, None))

    print(f"\n{'='*60}")
    print(f"Batch processing complete!")
    print(f"Successfully processed: {sum(1 for _, svg, _ in results if svg is not None)}/{len(csv_files)} files")
    print(f"{'='*60}")

    return results


def main():
    """Main function to handle both single file and batch processing."""
    # Check if there are CSV files in the current directory for batch processing
    work_dir = Path(r"C:\Users\<USER>\Documents\affaires\23I711.3 Strasbourg OD PL FCD\spatial_analysis_output\aggregated")
    csv_files = list(work_dir.glob("*.csv"))
    output_dir = work_dir / "output_flow_charts"
    os.makedirs(output_dir, exist_ok=True)

    if len(csv_files) == 1:
        # Single file mode - process the one CSV file found
        print("Single CSV file mode")
        csv_file = csv_files[0]
        svg_output, png_output = create_strasbourg_aggregation_flow(csv_file)
        print(f"Generated: {svg_output}")
        if png_output:
            print(f"Generated: {png_output}")

    elif len(csv_files) > 1:
        # Batch processing mode - process all CSV files
        print("Batch processing mode")
        results = process_csv_folder(work_dir, output_dir)

        # Print summary
        print("\nSummary of generated files:")
        for csv_file, svg_output, png_output in results:
            if svg_output:
                print(f"  {Path(csv_file).name} -> {Path(svg_output).name}")
                if png_output:
                    print(f"    + {Path(png_output).name}")

    else:
        # No CSV files found - use default file
        print("No CSV files found in current directory, using default file")
        default_csv = "hv_aggregated_od_matrix_total_RN4_Est_Ouest.csv"

        if Path(default_csv).exists():
            svg_output, png_output = create_strasbourg_aggregation_flow(default_csv)
            print(f"Generated: {svg_output}")
            if png_output:
                print(f"Generated: {png_output}")
        else:
            print(f"Default CSV file not found: {default_csv}")
            print("Please ensure CSV files are present in the current directory.")


if __name__ == "__main__":
    main()